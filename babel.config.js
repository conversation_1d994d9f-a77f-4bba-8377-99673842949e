module.exports = function (api) {
  api.cache(true);
  return {
    presets: [
      ['babel-preset-expo', { 
        jsxImportSource: undefined,
        native: {
          // Enable hermes for better performance
          unstable_transformProfile: 'hermes-stable'
        }
      }]
    ],
    plugins: [
      // Reanimated plugin (should be last)
      'react-native-reanimated/plugin',
    ],
    env: {
      production: {
        plugins: [
          // Remove console.log in production
          ['transform-remove-console', { exclude: ['error', 'warn'] }],
        ],
      },
    },
  };
};
