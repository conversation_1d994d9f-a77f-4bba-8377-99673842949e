const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Add support for additional file extensions
config.resolver.assetExts.push(
  // Audio formats
  'mp3',
  'wav',
  'aac',
  'm4a',
  'ogg',
  'flac',
  // Video formats
  'mp4',
  'mov',
  'avi',
  'mkv',
  // Image formats
  'webp',
  'svg',
  // Other formats
  'ttf',
  'otf',
  'woff',
  'woff2'
);

// Configure transformer for better performance
config.transformer.minifierConfig = {
  keep_fnames: true,
  mangle: {
    keep_fnames: true,
  },
};

// Enable hermes for better performance
config.transformer.hermesCommand = 'hermes';

module.exports = config;
