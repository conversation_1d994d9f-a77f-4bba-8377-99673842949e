{"name": "son-ykm", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "start:clear": "expo start --clear", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:apk": "eas build --platform android --profile preview", "test": "jest --watchAll", "lint": "expo lint", "clean": "expo install --fix"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "expo": "~52.0.37", "expo-av": "^15.0.2", "expo-blur": "~14.0.3", "expo-constants": "~17.0.7", "expo-font": "~13.0.4", "expo-linear-gradient": "^14.0.2", "expo-router": "~4.0.17", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.8", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.7", "react-native-safe-area-context": "4.12.0", "react-native-svg": "^15.11.2", "react-native-web": "~0.19.13", "expo-updates": "~0.27.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.4", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}