import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  SafeAreaView,
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Animated,
  Alert,
  AppState,
} from "react-native";
import Svg, { Defs, RadialGradient, Stop, Rect } from "react-native-svg";
import { BlurView } from "expo-blur";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { Audio } from "expo-av";
import { Easing } from "react-native";
import headphonesImg from "../assets/images/headphones.png";
import radioImg from "../assets/images/mobileapp-yakamoz.png";

const { width } = Dimensions.get("window");

// Optimized radio wave animation
const RadioWaves = ({ size = 300, color = "#7C4DFF" }) => {
  const wave1 = useRef(new Animated.Value(0)).current;
  const wave2 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const createWaveAnimation = (wave, delay = 0) => {
      return Animated.loop(
        Animated.timing(wave, {
          toValue: 1,
          duration: 3000,
          delay,
          easing: Easing.out(Easing.ease),
          useNativeDriver: true,
        })
      );
    };

    const wave1Animation = createWaveAnimation(wave1, 0);
    const wave2Animation = createWaveAnimation(wave2, 1500);

    wave1Animation.start();
    wave2Animation.start();

    return () => {
      wave1Animation.stop();
      wave2Animation.stop();
    };
  }, [wave1, wave2]);

  const getWaveStyle = (wave) => ({
    position: "absolute",
    width: size,
    height: size,
    borderRadius: size / 2,
    borderWidth: 2,
    borderColor: color,
    opacity: wave.interpolate({
      inputRange: [0, 1],
      outputRange: [0.6, 0],
    }),
    transform: [
      {
        scale: wave.interpolate({
          inputRange: [0, 1],
          outputRange: [0.8, 2.2],
        }),
      },
    ],
  });

  return (
    <View style={styles.waveContainer}>
      <Animated.View style={getWaveStyle(wave1)} />
      <Animated.View style={getWaveStyle(wave2)} />
    </View>
  );
};

const WAVE_BAR_COUNT = 20;

const App = () => {
  // Audio state
  const [sound, setSound] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [connectionError, setConnectionError] = useState(false);

  // UI state
  const [showPlayer, setShowPlayer] = useState(false);

  // Animation refs
  const fadeOutAnim = useRef(new Animated.Value(1)).current;
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const headphoneAnim = useRef(new Animated.Value(0)).current;
  const waveformAnim = useRef(new Animated.Value(0)).current;

  // Stream URL
  const STREAM_URL =
    "https://ssldyg.radyotvonline.com/smil/radyoyakamoz.stream/playlist.m3u8";

  // Headphone float animation
  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(headphoneAnim, {
          toValue: 1,
          duration: 2000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(headphoneAnim, {
          toValue: 0,
          duration: 2000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, [headphoneAnim]);

  // Audio configuration and cleanup
  useEffect(() => {
    const configureAudio = async () => {
      try {
        await Audio.setAudioModeAsync({
          staysActiveInBackground: true,
          playsInSilentModeIOS: true,
          interruptionModeIOS: Audio.INTERRUPTION_MODE_IOS_DO_NOT_MIX,
          interruptionModeAndroid: Audio.INTERRUPTION_MODE_ANDROID_DO_NOT_MIX,
          shouldDuckAndroid: false,
        });
      } catch (error) {
        console.warn("Audio configuration error:", error);
        setConnectionError(true);
      }
    };

    configureAudio();

    // Cleanup function
    return () => {
      if (sound) {
        sound.unloadAsync().catch(console.warn);
      }
    };
  }, [sound]);

  // Waveform animation
  useEffect(() => {
    if (isPlaying) {
      const animation = Animated.loop(
        Animated.sequence([
          Animated.timing(waveformAnim, {
            toValue: 1,
            duration: 1200,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: false,
          }),
          Animated.timing(waveformAnim, {
            toValue: 0,
            duration: 1200,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: false,
          }),
        ])
      );
      animation.start();
      return () => animation.stop();
    } else {
      waveformAnim.setValue(0);
    }
  }, [isPlaying, waveformAnim]);

  // Handle stream start with improved error handling
  const handleStart = useCallback(async () => {
    if (isLoading) return;

    try {
      setIsLoading(true);
      setConnectionError(false);

      // Clean up existing sound
      if (sound) {
        await sound.unloadAsync();
        setSound(null);
      }

      // Show player screen immediately with full opacity
      if (!showPlayer) {
        setShowPlayer(true);
        // Set fadeInAnim to 1 immediately for full visibility
        fadeInAnim.setValue(1);

        // Animate fadeOut for onboarding screen
        Animated.timing(fadeOutAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }

      // Create and configure audio
      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: STREAM_URL },
        {
          shouldPlay: true,
          progressUpdateIntervalMillis: 1000,
        }
      );

      // Set up status listener
      newSound.setOnPlaybackStatusUpdate((status) => {
        if (status.error) {
          console.warn("Playback error:", status.error);
          setIsPlaying(false);
          setConnectionError(true);
        } else if (status.isLoaded) {
          setIsPlaying(status.isPlaying);
          setConnectionError(false);
        }
      });

      setSound(newSound);
      setIsPlaying(true);
    } catch (error) {
      console.warn("Stream start error:", error);
      setIsPlaying(false);
      setConnectionError(true);
      Alert.alert(
        "Bağlantı Hatası",
        "Radyo çalınamıyor. İnternet bağlantınızı kontrol edin ve tekrar deneyin.",
        [{ text: "Tamam" }]
      );
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, sound, showPlayer, fadeOutAnim, fadeInAnim, STREAM_URL]);

  // Toggle playback with error handling
  const togglePlayback = useCallback(async () => {
    if (!sound) {
      handleStart();
      return;
    }

    try {
      const status = await sound.getStatusAsync();
      if (status.isLoaded) {
        if (status.isPlaying) {
          await sound.pauseAsync();
          setIsPlaying(false);
        } else {
          await sound.playAsync();
          setIsPlaying(true);
        }
      } else {
        // Sound is not loaded, restart
        handleStart();
      }
    } catch (error) {
      console.warn("Playback toggle error:", error);
      setConnectionError(true);
      handleStart();
    }
  }, [sound, handleStart]);

  // Optimized waveform rendering
  const renderWaveform = useCallback(() => {
    return (
      <View style={styles.waveform}>
        {[...Array(WAVE_BAR_COUNT)].map((_, i) => {
          const baseHeight = 15 + (i % 3) * 10;
          const animatedHeight = waveformAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [baseHeight, baseHeight + 25 + Math.sin(i) * 10],
          });

          return (
            <Animated.View
              key={i}
              style={[
                styles.waveBar,
                {
                  height: isPlaying ? animatedHeight : baseHeight,
                  opacity: isPlaying ? 1 : 0.3,
                },
              ]}
            />
          );
        })}
      </View>
    );
  }, [waveformAnim, isPlaying]);

  return (
    <SafeAreaView style={styles.container}>
      <BlurView intensity={100} style={StyleSheet.absoluteFill} tint="dark" />
      <Svg height="100%" width="100%" style={StyleSheet.absoluteFillObject}>
        <Defs>
          <RadialGradient id="grad" cx="50%" cy="50%" rx="80%" ry="50%">
            <Stop offset="0%" stopColor="#9668EF" stopOpacity="1" />
            <Stop offset="100%" stopColor="#100A1C" stopOpacity="1" />
          </RadialGradient>
        </Defs>
        <Rect width="100%" height="100%" fill="url(#grad)" />
      </Svg>

      {/* Onboarding Screen */}
      {!showPlayer && (
        <Animated.View style={[styles.content, { opacity: fadeOutAnim }]}>
          <RadioWaves size={300} color="#7C4DFF" />
          <Animated.Image
            source={headphonesImg}
            style={[
              styles.headphones,
              {
                transform: [
                  {
                    translateY: headphoneAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, -15],
                    }),
                  },
                ],
              },
            ]}
            resizeMode="contain"
          />
          <Text style={styles.bigTitle}>Yakamoz Dinle</Text>
          <Text style={styles.subtitle}>Yakamoz Dinlemeye Hazırmısınız?!</Text>
          <TouchableOpacity
            style={styles.startButton}
            onPress={handleStart}
            disabled={isLoading}
          >
            <LinearGradient
              colors={["#a18aff", "#7C4DFF"]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.gradientButton}
            >
              <Text style={styles.buttonText}>
                {isLoading ? "Yükleniyor..." : "Dinlemeye Başla"}
              </Text>
              <Ionicons
                name="musical-notes"
                size={22}
                color="#fff"
                style={{ marginLeft: 8 }}
              />
            </LinearGradient>
          </TouchableOpacity>
          <View style={styles.footer}>
            <Text style={styles.footerTitle}>M.B.Z.</Text>
            <Text style={styles.footerSubtitle}>yakamoz music app</Text>
          </View>
        </Animated.View>
      )}

      {/* Player Screen */}
      {showPlayer && (
        <View style={styles.playerContainer}>
          {/* Status and Control Buttons */}
          <View style={styles.topButtonsContainer}>
            <TouchableOpacity
              style={[
                styles.circleButton,
                connectionError && styles.errorButton,
              ]}
              onPress={handleStart}
              disabled={isLoading}
            >
              <Ionicons
                name={isLoading ? "hourglass" : "reload"}
                size={24}
                color="#FFFFFF"
              />
            </TouchableOpacity>
          </View>

          {/* Album Art */}
          <View style={styles.artworkContainer}>
            <Image source={radioImg} style={styles.playerArtwork} />
            {connectionError && (
              <View style={styles.errorOverlay}>
                <Ionicons name="wifi-off" size={32} color="#FF6B6B" />
                <Text style={styles.errorText}> Bağlantı Hatası</Text>
              </View>
            )}
          </View>

          {/* Track Info */}
          <View style={styles.trackInfo}>
            <Text style={styles.trackTitle}>Radiyo Yakamoz</Text>
            <Text style={styles.trackSubtitle}>
              {isLoading
                ? "Yükleniyor..."
                : connectionError
                ? "Bağlantı Hatası"
                : isPlaying
                ? "Çalışıyor..."
                : "Durdu"}
            </Text>
          </View>

          {/* Waveform */}
          <View style={styles.progressContainer}>{renderWaveform()}</View>

          {/* Playback Controls */}
          <View style={styles.controlsRow}>
            <TouchableOpacity
              onPress={handleStart}
              style={styles.iconButton}
              disabled={isLoading}
            >
              <Ionicons name="refresh" size={32} color="#FFFFFF" />
            </TouchableOpacity>

            <View style={styles.playButtonContainer}>
              <View
                style={[
                  styles.playButtonBackground,
                  isPlaying && styles.playingBackground,
                ]}
              />
              <TouchableOpacity onPress={togglePlayback} disabled={isLoading}>
                <Ionicons
                  name={isLoading ? "hourglass" : isPlaying ? "pause" : "play"}
                  size={48}
                  color="#FFFFFF"
                />
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              onPress={() =>
                Alert.alert("Bilgilendirme", "Radyo Yakamoz - Nostalji Müzik")
              }
              style={styles.iconButton}
            >
              <Ionicons name="information-circle" size={32} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  content: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  waveContainer: {
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
    zIndex: 0,
  },
  headphones: {
    width: width * 0.55,
    height: width * 0.55,
    marginBottom: 30,
    zIndex: 1,
  },
  bigTitle: {
    color: "#fff",
    fontSize: 32,
    fontWeight: "bold",
    marginTop: 10,
    textAlign: "center",
  },
  subtitle: {
    color: "#d1cfff",
    fontSize: 16,
    marginTop: 6,
    marginBottom: 40,
    textAlign: "center",
    paddingHorizontal: 20,
  },
  startButton: {
    marginTop: 10,
    borderRadius: 25,
    overflow: "hidden",
  },
  gradientButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 36,
    paddingVertical: 14,
    borderRadius: 25,
  },
  buttonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  footer: {
    position: "absolute",
    bottom: 32,
    width: "100%",
    alignItems: "center",
  },
  footerTitle: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 18,
    marginBottom: 2,
  },
  footerSubtitle: {
    color: "#d1cfff",
    fontSize: 13,
  },
  playerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  artworkContainer: {
    position: "relative",
    marginBottom: 20,
  },
  playerArtwork: {
    width: 280,
    height: 280,
    borderRadius: 16,
    alignSelf: "center",
  },
  errorOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.7)",
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    color: "#FF6B6B",
    fontSize: 14,
    marginTop: 8,
    textAlign: "center",
  },
  trackInfo: {
    alignItems: "center",
    marginBottom: 0,
  },
  trackTitle: {
    color: "#FFFFFF",
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 5,
  },
  trackSubtitle: {
    color: "rgba(255,255,255,0.8)",
    fontSize: 18,
  },
  progressContainer: {
    width: "100%",
    marginVertical: 5,
  },
  waveform: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
    height: 60,
    width: "100%",
    paddingHorizontal: 10,
  },
  waveBar: {
    width: 3,
    backgroundColor: "#FFFFFF",
    borderRadius: 2,
    marginHorizontal: 1,
  },
  controlsRow: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 40,
    gap: 32,
  },
  iconButton: {
    padding: 8,
  },
  playButtonContainer: {
    position: "relative",
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 24,
  },
  playButtonBackground: {
    position: "absolute",
    width: 80,
    height: 80,
    borderRadius: 40,
    opacity: 0.8,
    backgroundColor: "#7C4DFF",
    shadowColor: "#7C4DFF",
    shadowOpacity: 0.6,
    shadowRadius: 20,
    shadowOffset: { width: 0, height: 0 },
  },
  playingBackground: {
    backgroundColor: "#4CAF50",
    shadowColor: "#4CAF50",
  },
  topButtonsContainer: {
    position: "absolute",
    top: 20,
    left: 20,
    zIndex: 10,
  },
  circleButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: "rgba(124, 77, 255, 0.8)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  errorButton: {
    backgroundColor: "rgba(255, 107, 107, 0.8)",
  },
});

export default App;
