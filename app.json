{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "son-ykm", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.yakamoz.radio", "buildNumber": "1.0.0", "infoPlist": {"NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true, "NSExceptionDomains": {"radyotvonline.com": {"NSExceptionAllowsInsecureHTTPLoads": true, "NSExceptionMinimumTLSVersion": "1.0", "NSIncludesSubdomains": true}}}, "UIBackgroundModes": ["audio"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#7C4DFF"}, "permissions": ["INTERNET", "ACCESS_NETWORK_STATE", "WAKE_LOCK", "FOREGROUND_SERVICE", "MODIFY_AUDIO_SETTINGS"], "networkSecurityConfig": "./src/network_security_config.xml", "package": "com.yakamoz.radio", "usesCleartextTraffic": true, "allowBackup": false, "versionCode": 1, "compileSdkVersion": 34, "targetSdkVersion": 34, "minSdkVersion": 21, "buildToolsVersion": "34.0.0"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "baaad0d4-7e6c-4886-8531-77dcc0deaa0c"}}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/baaad0d4-7e6c-4886-8531-77dcc0deaa0c"}}}